/**
 * Enhanced Utilities Coordinator
 * Coordinates all enhanced WCAG utilities and provides unified interface
 */

import EnhancedSmartCache, {
  EnhancedCacheConfig,
  CacheWarmingPattern,
} from './enhanced-smart-cache';
import EnhancedBrowserPool, {
  EnhancedBrowserPoolConfig,
  WorkloadType,
} from './enhanced-browser-pool';
import EnhancedPerformanceMonitor, {
  PredictivePerformanceConfig,
} from './enhanced-performance-monitor';
import logger from '../../../utils/logger';

export interface EnhancedUtilitiesConfig {
  cache?: Partial<EnhancedCacheConfig>;
  browserPool?: Partial<EnhancedBrowserPoolConfig>;
  performanceMonitor?: Partial<PredictivePerformanceConfig>;
  enableIntegratedOptimization?: boolean;
  enableCrossUtilityAnalytics?: boolean;
  optimizationInterval?: number;
}

export interface UtilitiesHealthReport {
  cache: {
    status: 'healthy' | 'warning' | 'critical';
    hitRate: number;
    compressionRatio: number;
    recommendations: string[];
  };
  browserPool: {
    status: 'healthy' | 'warning' | 'critical';
    efficiency: number;
    healthyBrowsers: number;
    totalBrowsers: number;
    recommendations: string[];
  };
  performanceMonitor: {
    status: 'healthy' | 'warning' | 'critical';
    overallTrend: 'improving' | 'stable' | 'degrading';
    alertCount: number;
    recommendations: string[];
  };
  overall: {
    status: 'healthy' | 'warning' | 'critical';
    performanceScore: number;
    systemRecommendations: string[];
  };
}

export interface IntegratedOptimizationResult {
  cacheOptimizations: string[];
  browserPoolOptimizations: string[];
  performanceOptimizations: string[];
  crossUtilityOptimizations: string[];
  estimatedImpact: {
    performanceGain: number;
    memoryReduction: number;
    efficiencyImprovement: number;
  };
  implementedActions: string[];
}

/**
 * Coordinates all enhanced WCAG utilities for optimal performance
 */
export class EnhancedUtilitiesCoordinator {
  private static instance: EnhancedUtilitiesCoordinator;
  private cache: EnhancedSmartCache;
  private browserPool: EnhancedBrowserPool;
  private performanceMonitor: EnhancedPerformanceMonitor;
  private config: EnhancedUtilitiesConfig;
  private optimizationInterval: NodeJS.Timeout | null = null;
  private isInitialized = false;

  private constructor(config: EnhancedUtilitiesConfig = {}) {
    this.config = {
      enableIntegratedOptimization: config.enableIntegratedOptimization ?? true,
      enableCrossUtilityAnalytics: config.enableCrossUtilityAnalytics ?? true,
      optimizationInterval: config.optimizationInterval || 600000, // 10 minutes
      ...config,
    };

    // Initialize enhanced utilities
    this.cache = EnhancedSmartCache.getEnhancedInstance(this.config.cache);
    this.browserPool = EnhancedBrowserPool.getEnhancedInstance(this.config.browserPool);
    this.performanceMonitor = EnhancedPerformanceMonitor.getEnhancedInstance(
      this.config.performanceMonitor,
    );

    logger.info('🚀 Enhanced Utilities Coordinator initialized', {
      integratedOptimization: this.config.enableIntegratedOptimization,
      crossUtilityAnalytics: this.config.enableCrossUtilityAnalytics,
    });
  }

  static getInstance(config?: EnhancedUtilitiesConfig): EnhancedUtilitiesCoordinator {
    if (!EnhancedUtilitiesCoordinator.instance) {
      EnhancedUtilitiesCoordinator.instance = new EnhancedUtilitiesCoordinator(config);
    }
    return EnhancedUtilitiesCoordinator.instance;
  }

  /**
   * Initialize all enhanced utilities with cross-integration
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) {
      return;
    }

    logger.info('🔧 Initializing enhanced utilities integration');

    // Start integrated optimization if enabled
    if (this.config.enableIntegratedOptimization) {
      this.startIntegratedOptimization();
    }

    // Warm cache with intelligent patterns
    await this.intelligentCacheWarming();

    this.isInitialized = true;
    logger.info('✅ Enhanced utilities integration initialized');
  }

  /**
   * Get optimal page with integrated optimization
   */
  async getOptimalPage(scanId: string, workloadType?: Partial<WorkloadType>): Promise<any> {
    const defaultWorkload: WorkloadType = {
      type: 'medium',
      estimatedDuration: 30000,
      resourceIntensive: false,
      priority: 1,
      ...workloadType,
    };

    // Get performance insights to optimize workload
    const performanceStats = this.performanceMonitor.getEnhancedStats();
    if (performanceStats.trends?.overallTrend === 'degrading') {
      // Reduce workload intensity if performance is degrading
      defaultWorkload.type = defaultWorkload.type === 'heavy' ? 'medium' : 'light';
    }

    return this.browserPool.getOptimalPage(defaultWorkload, scanId);
  }

  /**
   * Enhanced cache operations with performance monitoring
   */
  async cacheWithMonitoring<T>(
    key: string,
    data: T,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
    ttl?: number,
  ): Promise<void> {
    const startTime = Date.now();

    await this.cache.setCompressed(key, data, cacheType, ttl);

    const duration = Date.now() - startTime;
    if (duration > 100) {
      // Log slow cache operations
      logger.warn(`⚠️ Slow cache operation: ${key} took ${duration}ms`);
    }
  }

  async getCacheWithMonitoring<T>(
    key: string,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
  ): Promise<T | null> {
    const startTime = Date.now();

    const result = await this.cache.getCompressed<T>(key, cacheType);

    const duration = Date.now() - startTime;
    if (duration > 50) {
      // Log slow cache retrievals
      logger.warn(`⚠️ Slow cache retrieval: ${key} took ${duration}ms`);
    }

    return result;
  }

  /**
   * Comprehensive health check of all utilities
   */
  async getHealthReport(): Promise<UtilitiesHealthReport> {
    const cacheStats = this.cache.getEnhancedStats();
    const browserStats = this.browserPool.getEnhancedStats();
    const performanceStats = this.performanceMonitor.getEnhancedStats();

    // Analyze cache health
    const cacheStatus = this.analyzeCacheHealth(cacheStats);
    const browserStatus = this.analyzeBrowserPoolHealth(browserStats);
    const performanceStatus = this.analyzePerformanceHealth(performanceStats);

    // Determine overall health
    const overallStatus = this.determineOverallHealth([
      cacheStatus.status,
      browserStatus.status,
      performanceStatus.status,
    ]);

    const overallScore = this.calculateOverallPerformanceScore(
      cacheStats,
      browserStats,
      performanceStats,
    );

    return {
      cache: cacheStatus,
      browserPool: browserStatus,
      performanceMonitor: performanceStatus,
      overall: {
        status: overallStatus,
        performanceScore: overallScore,
        systemRecommendations: this.generateSystemRecommendations(
          cacheStats,
          browserStats,
          performanceStats,
        ),
      },
    };
  }

  /**
   * Integrated optimization across all utilities
   */
  async performIntegratedOptimization(): Promise<IntegratedOptimizationResult> {
    logger.info('🔧 Starting integrated optimization');

    const cacheOptimizations: string[] = [];
    const browserPoolOptimizations: string[] = [];
    const performanceOptimizations: string[] = [];
    const crossUtilityOptimizations: string[] = [];
    const implementedActions: string[] = [];

    // Get current stats
    const cacheStats = this.cache.getEnhancedStats();
    const browserStats = this.browserPool.getEnhancedStats();
    const performanceStats = this.performanceMonitor.getEnhancedStats();

    // Cache optimizations
    if (cacheStats.analytics.compressionRatio < 1.5) {
      cacheOptimizations.push('Increase compression threshold for better efficiency');
    }
    if (cacheStats.hitRate < 70) {
      cacheOptimizations.push('Implement predictive cache warming');
      await this.intelligentCacheWarming();
      implementedActions.push('Executed intelligent cache warming');
    }

    // Browser pool optimizations
    if (browserStats.loadBalancing.balancingEfficiency < 70) {
      browserPoolOptimizations.push('Optimize load balancing strategy');
    }
    const unhealthyBrowsers = browserStats.healthMetrics.filter((b) => !b.isHealthy).length;
    if (unhealthyBrowsers > 0) {
      browserPoolOptimizations.push(`Recover ${unhealthyBrowsers} unhealthy browsers`);
    }

    // Performance optimizations
    if (performanceStats.trends?.overallTrend === 'degrading') {
      performanceOptimizations.push('Address performance degradation trend');
    }
    if (performanceStats.recentAlerts.filter((a) => a.severity === 'critical').length > 0) {
      performanceOptimizations.push('Address critical performance alerts');
    }

    // Cross-utility optimizations
    if (this.config.enableCrossUtilityAnalytics) {
      crossUtilityOptimizations.push(
        ...this.analyzeCrossUtilityOptimizations(cacheStats, browserStats, performanceStats),
      );
    }

    // Estimate impact
    const estimatedImpact = {
      performanceGain: this.estimateIntegratedPerformanceGain(
        cacheOptimizations,
        browserPoolOptimizations,
        performanceOptimizations,
      ),
      memoryReduction: this.estimateIntegratedMemoryReduction(
        cacheOptimizations,
        browserPoolOptimizations,
      ),
      efficiencyImprovement:
        this.estimateIntegratedEfficiencyImprovement(crossUtilityOptimizations),
    };

    logger.info('✅ Integrated optimization completed', {
      cacheOptimizations: cacheOptimizations.length,
      browserOptimizations: browserPoolOptimizations.length,
      performanceOptimizations: performanceOptimizations.length,
      crossUtilityOptimizations: crossUtilityOptimizations.length,
      implementedActions: implementedActions.length,
    });

    return {
      cacheOptimizations,
      browserPoolOptimizations,
      performanceOptimizations,
      crossUtilityOptimizations,
      estimatedImpact,
      implementedActions,
    };
  }

  /**
   * Intelligent cache warming based on scan patterns
   */
  private async intelligentCacheWarming(): Promise<void> {
    // Get recent scan history from performance monitor
    const performanceStats = this.performanceMonitor.getEnhancedStats();

    // Generate warming patterns based on performance data
    const patterns: CacheWarmingPattern[] = [
      {
        type: 'rule',
        pattern: 'WCAG-004', // Contrast checks are frequently used
        priority: 10,
        frequency: 8,
      },
      {
        type: 'rule',
        pattern: 'WCAG-001', // Alt text checks are common
        priority: 9,
        frequency: 7,
      },
      {
        type: 'pattern',
        pattern: 'common-selectors',
        priority: 8,
        frequency: 6,
      },
    ];

    await this.cache.warmCache(patterns);
  }

  private startIntegratedOptimization(): void {
    this.optimizationInterval = setInterval(async () => {
      try {
        await this.performIntegratedOptimization();
      } catch (error) {
        logger.error('❌ Integrated optimization failed:', error);
      }
    }, this.config.optimizationInterval);
  }

  private analyzeCacheHealth(stats: any): UtilitiesHealthReport['cache'] {
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    if (stats.hitRate < 50) {
      status = 'critical';
      recommendations.push('Cache hit rate critically low - review caching strategy');
    } else if (stats.hitRate < 70) {
      status = 'warning';
      recommendations.push('Cache hit rate below optimal - consider cache warming');
    }

    if (stats.analytics.compressionRatio < 1.2) {
      recommendations.push('Compression efficiency low - adjust compression settings');
    }

    return {
      status,
      hitRate: stats.hitRate || 0,
      compressionRatio: stats.analytics?.compressionRatio || 1,
      recommendations,
    };
  }

  private analyzeBrowserPoolHealth(stats: any): UtilitiesHealthReport['browserPool'] {
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    const healthyBrowsers = stats.healthMetrics?.filter((b: any) => b.isHealthy).length || 0;
    const totalBrowsers = stats.healthMetrics?.length || 0;
    const efficiency = stats.loadBalancing?.balancingEfficiency || 0;

    if (healthyBrowsers / totalBrowsers < 0.5) {
      status = 'critical';
      recommendations.push(
        'More than half of browsers are unhealthy - immediate attention required',
      );
    } else if (healthyBrowsers / totalBrowsers < 0.8) {
      status = 'warning';
      recommendations.push('Some browsers are unhealthy - monitor and recover');
    }

    if (efficiency < 50) {
      status = 'critical';
      recommendations.push('Load balancing efficiency critically low');
    } else if (efficiency < 70) {
      if (status === 'healthy') status = 'warning';
      recommendations.push('Load balancing efficiency below optimal');
    }

    return {
      status,
      efficiency,
      healthyBrowsers,
      totalBrowsers,
      recommendations,
    };
  }

  private analyzePerformanceHealth(stats: any): UtilitiesHealthReport['performanceMonitor'] {
    const recommendations: string[] = [];
    let status: 'healthy' | 'warning' | 'critical' = 'healthy';

    const overallTrend = stats.trends?.overallTrend || 'stable';
    const criticalAlerts =
      stats.recentAlerts?.filter((a: any) => a.severity === 'critical').length || 0;
    const alertCount = stats.recentAlerts?.length || 0;

    if (overallTrend === 'degrading') {
      status = 'warning';
      recommendations.push('Performance is degrading - investigate bottlenecks');
    }

    if (criticalAlerts > 0) {
      status = 'critical';
      recommendations.push(`${criticalAlerts} critical alerts require immediate attention`);
    } else if (alertCount > 5) {
      if (status === 'healthy') status = 'warning';
      recommendations.push('High number of performance alerts - review system health');
    }

    return {
      status,
      overallTrend,
      alertCount,
      recommendations,
    };
  }

  private determineOverallHealth(
    statuses: Array<'healthy' | 'warning' | 'critical'>,
  ): 'healthy' | 'warning' | 'critical' {
    if (statuses.includes('critical')) return 'critical';
    if (statuses.includes('warning')) return 'warning';
    return 'healthy';
  }

  private calculateOverallPerformanceScore(
    cacheStats: any,
    browserStats: any,
    performanceStats: any,
  ): number {
    const cacheScore = (cacheStats.hitRate || 0) * 0.3;
    const browserScore = (browserStats.loadBalancing?.balancingEfficiency || 0) * 0.3;
    const performanceScore = (performanceStats.baseStats?.performanceScore || 0) * 0.4;

    return Math.round(cacheScore + browserScore + performanceScore);
  }

  private generateSystemRecommendations(
    cacheStats: any,
    browserStats: any,
    performanceStats: any,
  ): string[] {
    const recommendations: string[] = [];

    // System-wide recommendations based on cross-utility analysis
    if (
      (cacheStats.hitRate || 0) < 70 &&
      (browserStats.loadBalancing?.balancingEfficiency || 0) < 70
    ) {
      recommendations.push(
        'Both cache and browser pool efficiency are low - consider system resource optimization',
      );
    }

    if (performanceStats.trends?.overallTrend === 'degrading') {
      recommendations.push(
        'Overall performance degrading - implement proactive monitoring and optimization',
      );
    }

    if (performanceStats.recentAlerts?.length > 10) {
      recommendations.push(
        'High alert volume - review system thresholds and optimization strategies',
      );
    }

    return recommendations;
  }

  private analyzeCrossUtilityOptimizations(
    cacheStats: any,
    browserStats: any,
    performanceStats: any,
  ): string[] {
    const optimizations: string[] = [];

    // Analyze correlations between utilities
    const lowCacheHitRate = (cacheStats.hitRate || 0) < 70;
    const lowBrowserEfficiency = (browserStats.loadBalancing?.balancingEfficiency || 0) < 70;
    const performanceDegrading = performanceStats.trends?.overallTrend === 'degrading';

    if (lowCacheHitRate && performanceDegrading) {
      optimizations.push('Improve cache warming to reduce performance impact of cache misses');
    }

    if (lowBrowserEfficiency && performanceDegrading) {
      optimizations.push('Optimize browser pool allocation to improve overall performance');
    }

    if (lowCacheHitRate && lowBrowserEfficiency) {
      optimizations.push('Coordinate cache and browser pool optimization for maximum efficiency');
    }

    // Memory correlation analysis
    const highMemoryUsage = performanceStats.baseStats?.memoryPeakMB > 1000;
    if (highMemoryUsage && (cacheStats.analytics?.compressionRatio || 1) < 1.5) {
      optimizations.push('Increase cache compression to reduce memory pressure');
    }

    return optimizations;
  }

  private estimateIntegratedPerformanceGain(
    cacheOpts: string[],
    browserOpts: string[],
    perfOpts: string[],
  ): number {
    let gain = 0;

    // Cache optimizations impact
    gain += cacheOpts.length * 8;

    // Browser pool optimizations impact
    gain += browserOpts.length * 12;

    // Performance optimizations impact
    gain += perfOpts.length * 10;

    // Synergy bonus for multiple optimizations
    const totalOpts = cacheOpts.length + browserOpts.length + perfOpts.length;
    if (totalOpts > 3) {
      gain += totalOpts * 2; // Synergy bonus
    }

    return Math.min(gain, 60); // Cap at 60%
  }

  private estimateIntegratedMemoryReduction(cacheOpts: string[], browserOpts: string[]): number {
    let reduction = 0;

    // Cache compression and cleanup
    reduction +=
      cacheOpts.filter((opt) => opt.includes('compression') || opt.includes('warming')).length * 15;

    // Browser pool memory management
    reduction +=
      browserOpts.filter((opt) => opt.includes('memory') || opt.includes('cleanup')).length * 20;

    return Math.min(reduction, 45); // Cap at 45%
  }

  private estimateIntegratedEfficiencyImprovement(crossUtilityOpts: string[]): number {
    // Cross-utility optimizations have higher impact on efficiency
    return Math.min(crossUtilityOpts.length * 15, 50); // Cap at 50%
  }

  /**
   * Get access to individual enhanced utilities
   */
  getCache(): EnhancedSmartCache {
    return this.cache;
  }

  getBrowserPool(): EnhancedBrowserPool {
    return this.browserPool;
  }

  getPerformanceMonitor(): EnhancedPerformanceMonitor {
    return this.performanceMonitor;
  }

  /**
   * Shutdown all enhanced utilities
   */
  async shutdown(): Promise<void> {
    logger.info('🔄 Shutting down enhanced utilities coordinator');

    // Clear optimization interval
    if (this.optimizationInterval) {
      clearInterval(this.optimizationInterval);
    }

    // Shutdown individual utilities
    await Promise.all([this.browserPool.shutdown(), this.performanceMonitor.shutdown()]);

    this.isInitialized = false;
    logger.info('✅ Enhanced utilities coordinator shutdown completed');
  }

  /**
   * Backward compatibility methods
   */
  async getPage(scanId: string): Promise<any> {
    return this.getOptimalPage(scanId);
  }

  async cacheData<T>(
    key: string,
    data: T,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
    ttl?: number,
  ): Promise<void> {
    return this.cacheWithMonitoring(key, data, cacheType, ttl);
  }

  async getCachedData<T>(
    key: string,
    cacheType: 'dom' | 'rule' | 'pattern' | 'site',
  ): Promise<T | null> {
    return this.getCacheWithMonitoring(key, cacheType);
  }

  startScanMonitoring(scanId: string): void {
    this.performanceMonitor.startScanMonitoring(scanId);
  }

  recordCheckStart(scanId: string, ruleId: string, ruleName: string): void {
    this.performanceMonitor.recordCheckStart(scanId, ruleId, ruleName);
  }

  recordCheckEnd(scanId: string, ruleId: string, success: boolean, errorMessage?: string): void {
    this.performanceMonitor.recordCheckEnd(scanId, ruleId, success, errorMessage);
  }

  completeScanMonitoring(scanId: string): any {
    return this.performanceMonitor.completeScanMonitoring(scanId);
  }
}

export default EnhancedUtilitiesCoordinator;
