/**
 * Utility Integration Manager
 * Centralized manager for integrating all WCAG utilities into checks
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';
import AISemanticValidator from './ai-semantic-validator';
import AccessibilityPatternLibrary from './accessibility-pattern-library';
import ContentQualityAnalyzer from './content-quality-analyzer';
import ModernFrameworkOptimizer from './modern-framework-optimizer';
import ComponentLibraryDetector from './component-library-detector';
import HeadlessCMSDetector from './headless-cms-detector';

export interface UtilityIntegrationConfig {
  // Utility enablement flags
  enableSemanticValidation?: boolean;
  enablePatternValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableFrameworkOptimization?: boolean;
  enableComponentLibraryDetection?: boolean;
  enableCMSDetection?: boolean;

  // Integration strategy
  integrationStrategy?: 'supplement' | 'enhance' | 'validate';

  // Performance settings
  enableCaching?: boolean;
  maxExecutionTime?: number;

  // Fallback settings
  enableGracefulFallback?: boolean;
  fallbackOnError?: boolean;

  // Priority-based assignment
  priority?: 'high' | 'medium' | 'low';
  category?: 'content' | 'interactive' | 'visual' | 'input' | 'framework' | 'specialized';
}

export interface UtilityAnalysisResult {
  semanticAnalysis?: any;
  patternAnalysis?: any;
  contentQualityAnalysis?: any;
  frameworkAnalysis?: any;
  componentLibraryAnalysis?: any;
  cmsAnalysis?: any;

  // Integration metadata
  executionTime: number;
  utilitiesUsed: string[];
  errors: string[];
  recommendations: string[];

  // Enhanced scoring
  utilityConfidence: number;
  enhancedAccuracy: number;
}

export interface CheckEnhancementResult {
  originalScore: number;
  enhancedScore: number;
  confidenceBoost: number;
  additionalEvidence: any[];
  utilityRecommendations: string[];
  frameworkSpecificGuidance: string[];
}

export interface UtilityExecutionContext {
  scanId?: string;
  ruleId: string;
  startTime: number;
  endTime?: number;
  utilitiesUsed: string[];
  cacheHits: number;
  cacheMisses: number;
  errors: string[];
  integrationStrategy: 'supplement' | 'enhance' | 'validate';
}

/**
 * Priority mapping for different check types
 */
export const CHECK_UTILITY_PRIORITY: Record<string, UtilityIntegrationConfig> = {
  // High Priority - Content and Semantic Checks
  'WCAG-001': {
    // Non-text Content
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableContentQualityAnalysis: true,
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-003': {
    // Info and Relationships
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableContentQualityAnalysis: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-025': {
    // Landmarks
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },

  // Medium Priority - Interactive Elements
  'WCAG-007': {
    // Focus Visible
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-014': {
    // Target Size
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },

  // Framework-Specific Checks
  'WCAG-009': {
    // Name, Role, Value
    enableSemanticValidation: true,
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },

  // Content Quality Checks
  'WCAG-031': {
    // Page Titled
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'supplement',
  },

  // ===== CATEGORY 1: CONTENT & SEMANTIC CHECKS =====
  // High Priority - Full Enhancement
  'WCAG-002': {
    // Captions (Prerecorded)
    enableSemanticValidation: true,
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'enhance',
    maxExecutionTime: 8000,
  },
  'WCAG-017': {
    // Image Alternatives
    enableSemanticValidation: true,
    enableContentQualityAnalysis: true,
    enablePatternValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-018': {
    // Text and Wording
    enableContentQualityAnalysis: true,
    enableSemanticValidation: true,
    enableCMSDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-024': {
    // Language of Page
    enableContentQualityAnalysis: true,
    enablePatternValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-029': {
    // Page Titled
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-030': {
    // Labels or Instructions
    enableSemanticValidation: true,
    enablePatternValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-033': {
    // Audio-only and Video-only
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-034': {
    // Audio Description
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-036': {
    // Headings and Labels
    enableSemanticValidation: true,
    enablePatternValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-038': {
    // Language of Parts
    enableContentQualityAnalysis: true,
    enableSemanticValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-060': {
    // Unusual Words
    enableContentQualityAnalysis: true,
    enableSemanticValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-061': {
    // Abbreviations
    enableContentQualityAnalysis: true,
    enableSemanticValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-062': {
    // Reading Level
    enableContentQualityAnalysis: true,
    enableSemanticValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-063': {
    // Pronunciation
    enableContentQualityAnalysis: true,
    enableSemanticValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-065': {
    // Help
    enableContentQualityAnalysis: true,
    enableCMSDetection: true,
    integrationStrategy: 'supplement',
  },

  // ===== CATEGORY 2: INTERACTIVE & FOCUS CHECKS =====
  // High Priority - Full Enhancement
  'WCAG-006': {
    // Focus Order
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-010': {
    // Focus Not Obscured (Minimum)
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-011': {
    // Focus Not Obscured (Enhanced)
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-012': {
    // Focus Appearance
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-013': {
    // Dragging Movements
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-019': {
    // Keyboard Focus
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-026': {
    // Link Purpose (In Context)
    enablePatternValidation: true,
    enableSemanticValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-027': {
    // No Keyboard Trap
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-028': {
    // Bypass Blocks
    enablePatternValidation: true,
    enableSemanticValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-051': {
    // Keyboard Accessible
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-052': {
    // Character Key Shortcuts
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-055': {
    // Label in Name
    enablePatternValidation: true,
    enableSemanticValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-058': {
    // Target Size Enhanced
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },

  // ===== CATEGORY 3: VISUAL & COLOR CHECKS =====
  // High Priority - Full Enhancement
  'WCAG-004': {
    // Contrast (Minimum) - Enhanced from existing
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-037': {
    // Resize Text
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-039': {
    // Images of Text
    enableContentQualityAnalysis: true,
    enablePatternValidation: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-040': {
    // Reflow
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-041': {
    // Non-text Contrast
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-042': {
    // Text Spacing
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-043': {
    // Content on Hover or Focus
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-050': {
    // Audio Control
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },

  // ===== CATEGORY 4: INPUT & FORM CHECKS =====
  // High Priority - Full Enhancement
  'WCAG-008': {
    // Error Identification
    enablePatternValidation: true,
    enableSemanticValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-015': {
    // Consistent Help
    enablePatternValidation: true,
    enableContentQualityAnalysis: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-016': {
    // Redundant Entry
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-022': {
    // Accessible Authentication (Minimum)
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-023': {
    // Accessible Authentication (Enhanced)
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-032': {
    // Error Prevention
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-064': {
    // Change on Request
    enablePatternValidation: true,
    enableFrameworkOptimization: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-066': {
    // Error Prevention Enhanced
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-053': {
    // Pointer Gestures
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-054': {
    // Pointer Cancellation
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },
  'WCAG-056': {
    // Motion Actuation
    enablePatternValidation: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'enhance',
  },

  // ===== CATEGORY 5: FRAMEWORK & TECHNICAL CHECKS =====
  // Medium Priority - Supplement Strategy
  'WCAG-020': {
    // Motor
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    enablePatternValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-021': {
    // Pronunciation & Meaning
    enableFrameworkOptimization: true,
    enableContentQualityAnalysis: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-035': {
    // Multiple Ways
    enableFrameworkOptimization: true,
    enablePatternValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-044': {
    // Timing Adjustable
    enableFrameworkOptimization: true,
    enablePatternValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-045': {
    // Pause, Stop, Hide
    enableFrameworkOptimization: true,
    enablePatternValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-046': {
    // Three Flashes or Below Threshold
    enableFrameworkOptimization: true,
    enablePatternValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-057': {
    // Status Messages
    enableFrameworkOptimization: true,
    enablePatternValidation: true,
    enableSemanticValidation: true,
    integrationStrategy: 'supplement',
  },
  'WCAG-059': {
    // Concurrent Input Mechanisms
    enableFrameworkOptimization: true,
    enableComponentLibraryDetection: true,
    integrationStrategy: 'supplement',
  },

  // Default configuration for other checks
  default: {
    enableSemanticValidation: false,
    enablePatternValidation: true,
    enableContentQualityAnalysis: false,
    enableFrameworkOptimization: false,
    enableComponentLibraryDetection: false,
    enableCMSDetection: false,
    integrationStrategy: 'supplement',
    enableCaching: true,
    maxExecutionTime: 5000,
    enableGracefulFallback: true,
    fallbackOnError: true,
  },
};

/**
 * Utility Integration Manager
 */
export class UtilityIntegrationManager {
  private static instance: UtilityIntegrationManager;

  // Utility instances
  private semanticValidator: AISemanticValidator;
  private patternLibrary: AccessibilityPatternLibrary;
  private contentAnalyzer: ContentQualityAnalyzer;
  private frameworkOptimizer: ModernFrameworkOptimizer;
  private componentDetector: ComponentLibraryDetector;
  private cmsDetector: HeadlessCMSDetector;

  // Enhanced caching system
  private utilityCache = new Map<
    string,
    {
      result: any;
      timestamp: number;
      ttl: number;
      ruleId: string;
    }
  >();

  // Performance monitoring
  private performanceMetrics = new Map<
    string,
    {
      executionCount: number;
      totalExecutionTime: number;
      averageExecutionTime: number;
      lastExecutionTime: number;
      errorCount: number;
      cacheHits: number;
      cacheMisses: number;
    }
  >();

  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_CACHE_SIZE = 1000; // Maximum cache entries

  private constructor() {
    this.semanticValidator = AISemanticValidator.getInstance();
    this.patternLibrary = AccessibilityPatternLibrary.getInstance();
    this.contentAnalyzer = ContentQualityAnalyzer.getInstance();
    this.frameworkOptimizer = ModernFrameworkOptimizer.getModernInstance();
    this.componentDetector = ComponentLibraryDetector.getInstance();
    this.cmsDetector = HeadlessCMSDetector.getInstance();

    logger.info('🔧 Utility Integration Manager initialized');
  }

  static getInstance(): UtilityIntegrationManager {
    if (!UtilityIntegrationManager.instance) {
      UtilityIntegrationManager.instance = new UtilityIntegrationManager();
    }
    return UtilityIntegrationManager.instance;
  }

  /**
   * Get utility configuration for a specific check
   */
  getUtilityConfig(ruleId: string): UtilityIntegrationConfig {
    return CHECK_UTILITY_PRIORITY[ruleId] || CHECK_UTILITY_PRIORITY['default'];
  }

  /**
   * Get priority-based utility configuration
   */
  getPriorityBasedConfig(
    ruleId: string,
    performanceMode?: 'high' | 'balanced' | 'conservative',
  ): UtilityIntegrationConfig {
    const baseConfig = this.getUtilityConfig(ruleId);
    const mode = performanceMode || 'balanced';

    // Adjust configuration based on performance mode
    switch (mode) {
      case 'high':
        return {
          ...baseConfig,
          maxExecutionTime: (baseConfig.maxExecutionTime || 5000) * 1.5,
          enableCaching: true,
        };
      case 'conservative':
        return {
          ...baseConfig,
          maxExecutionTime: Math.min(baseConfig.maxExecutionTime || 5000, 3000),
          enableGracefulFallback: true,
          fallbackOnError: true,
        };
      default: // balanced
        return baseConfig;
    }
  }

  /**
   * Get checks by priority tier
   */
  getChecksByPriority(priority: 'high' | 'medium' | 'low'): string[] {
    const priorityMapping = {
      high: [
        // Category 1: Content & Semantic (High Priority)
        'WCAG-001',
        'WCAG-002',
        'WCAG-003',
        'WCAG-017',
        'WCAG-018',
        'WCAG-030',
        'WCAG-036',
        'WCAG-038',
        // Category 2: Interactive & Focus (Critical)
        'WCAG-006',
        'WCAG-007',
        'WCAG-010',
        'WCAG-011',
        'WCAG-012',
        'WCAG-013',
        'WCAG-019',
        'WCAG-026',
        'WCAG-027',
        'WCAG-028',
        // Category 3: Visual & Color (Critical)
        'WCAG-004',
        'WCAG-037',
        'WCAG-040',
        'WCAG-041',
        'WCAG-042',
        'WCAG-043',
      ],
      medium: [
        // Remaining Category 1: Content & Semantic
        'WCAG-024',
        'WCAG-029',
        'WCAG-033',
        'WCAG-034',
        'WCAG-065',
        // Remaining Category 2: Interactive & Focus
        'WCAG-051',
        'WCAG-052',
        'WCAG-055',
        'WCAG-058',
        // Remaining Category 3: Visual & Color
        'WCAG-039',
        'WCAG-050',
        // Category 4: Input & Form
        'WCAG-008',
        'WCAG-015',
        'WCAG-016',
        'WCAG-022',
        'WCAG-023',
        'WCAG-032',
        'WCAG-053',
        'WCAG-054',
        'WCAG-056',
        'WCAG-066',
      ],
      low: [
        // Category 1: Content & Semantic (Low Priority)
        'WCAG-060',
        'WCAG-061',
        'WCAG-062',
        'WCAG-063',
        // Category 4: Input & Form (Low Priority)
        'WCAG-064',
        // Category 5: Framework & Technical
        'WCAG-009',
        'WCAG-020',
        'WCAG-021',
        'WCAG-035',
        'WCAG-044',
        'WCAG-045',
        'WCAG-046',
        'WCAG-057',
        'WCAG-059',
        // Content Quality
        'WCAG-031',
      ],
    };

    return priorityMapping[priority] || [];
  }

  /**
   * Get utility integration statistics
   */
  getIntegrationStatistics(): {
    totalChecks: number;
    configuredChecks: number;
    defaultChecks: number;
    byPriority: Record<string, number>;
    byCategory: Record<string, number>;
    byStrategy: Record<string, number>;
  } {
    const allRuleIds = Object.keys(CHECK_UTILITY_PRIORITY).filter((key) => key !== 'default');
    const totalChecks = 69; // Total WCAG checks
    const configuredChecks = allRuleIds.length;
    const defaultChecks = totalChecks - configuredChecks;

    const byPriority = {
      high: this.getChecksByPriority('high').length,
      medium: this.getChecksByPriority('medium').length,
      low: this.getChecksByPriority('low').length,
    };

    const byStrategy = allRuleIds.reduce(
      (acc, ruleId) => {
        const config = CHECK_UTILITY_PRIORITY[ruleId];
        const strategy = config.integrationStrategy || 'supplement';
        acc[strategy] = (acc[strategy] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const byCategory = {
      content: 18,
      interactive: 15,
      visual: 8,
      input: 12,
      framework: 9,
      specialized: 7,
    };

    return {
      totalChecks,
      configuredChecks,
      defaultChecks,
      byPriority,
      byCategory,
      byStrategy,
    };
  }

  /**
   * Validate utility integration configuration
   */
  validateConfiguration(): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
    recommendations: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const recommendations: string[] = [];

    // Check for missing configurations
    const stats = this.getIntegrationStatistics();
    if (stats.defaultChecks > 0) {
      warnings.push(`${stats.defaultChecks} checks are using default configuration`);
    }

    // Check for performance optimization opportunities
    const highPriorityChecks = this.getChecksByPriority('high');
    highPriorityChecks.forEach((ruleId) => {
      const config = this.getUtilityConfig(ruleId);
      if (!config.enableCaching) {
        recommendations.push(`Enable caching for high-priority check ${ruleId}`);
      }
      if ((config.maxExecutionTime || 5000) > 8000) {
        warnings.push(`Check ${ruleId} has high execution time: ${config.maxExecutionTime}ms`);
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      recommendations,
    };
  }

  /**
   * Enhanced caching with performance monitoring
   */
  private getCachedResult(cacheKey: string, ruleId: string): any | null {
    const cached = this.utilityCache.get(cacheKey);

    if (cached && Date.now() - cached.timestamp < cached.ttl) {
      // Cache hit
      this.updatePerformanceMetrics(ruleId, 'cacheHit');
      return cached.result;
    }

    // Cache miss or expired
    if (cached) {
      this.utilityCache.delete(cacheKey);
    }
    this.updatePerformanceMetrics(ruleId, 'cacheMiss');
    return null;
  }

  /**
   * Cache utility result with TTL
   */
  private setCachedResult(cacheKey: string, result: any, ruleId: string, customTtl?: number): void {
    // Implement cache size limit
    if (this.utilityCache.size >= this.MAX_CACHE_SIZE) {
      // Remove oldest entries (simple LRU)
      const oldestKey = this.utilityCache.keys().next().value;
      this.utilityCache.delete(oldestKey);
    }

    this.utilityCache.set(cacheKey, {
      result,
      timestamp: Date.now(),
      ttl: customTtl || this.CACHE_TTL,
      ruleId,
    });
  }

  /**
   * Update performance metrics
   */
  private updatePerformanceMetrics(
    ruleId: string,
    type: 'execution' | 'error' | 'cacheHit' | 'cacheMiss',
    executionTime?: number,
  ): void {
    if (!this.performanceMetrics.has(ruleId)) {
      this.performanceMetrics.set(ruleId, {
        executionCount: 0,
        totalExecutionTime: 0,
        averageExecutionTime: 0,
        lastExecutionTime: 0,
        errorCount: 0,
        cacheHits: 0,
        cacheMisses: 0,
      });
    }

    const metrics = this.performanceMetrics.get(ruleId)!;

    switch (type) {
      case 'execution':
        metrics.executionCount++;
        if (executionTime) {
          metrics.totalExecutionTime += executionTime;
          metrics.averageExecutionTime = metrics.totalExecutionTime / metrics.executionCount;
          metrics.lastExecutionTime = executionTime;
        }
        break;
      case 'error':
        metrics.errorCount++;
        break;
      case 'cacheHit':
        metrics.cacheHits++;
        break;
      case 'cacheMiss':
        metrics.cacheMisses++;
        break;
    }
  }

  /**
   * Get performance metrics for a specific rule or all rules
   */
  getPerformanceMetrics(ruleId?: string): any {
    if (ruleId) {
      return this.performanceMetrics.get(ruleId) || null;
    }

    // Return aggregated metrics for all rules
    const allMetrics = Array.from(this.performanceMetrics.entries());
    return {
      totalRules: allMetrics.length,
      totalExecutions: allMetrics.reduce((sum, [, metrics]) => sum + metrics.executionCount, 0),
      totalErrors: allMetrics.reduce((sum, [, metrics]) => sum + metrics.errorCount, 0),
      totalCacheHits: allMetrics.reduce((sum, [, metrics]) => sum + metrics.cacheHits, 0),
      totalCacheMisses: allMetrics.reduce((sum, [, metrics]) => sum + metrics.cacheMisses, 0),
      averageExecutionTime:
        allMetrics.reduce((sum, [, metrics]) => sum + metrics.averageExecutionTime, 0) /
        allMetrics.length,
      cacheHitRate:
        allMetrics.reduce((sum, [, metrics]) => sum + metrics.cacheHits, 0) /
        (allMetrics.reduce(
          (sum, [, metrics]) => sum + metrics.cacheHits + metrics.cacheMisses,
          0,
        ) || 1),
      ruleMetrics: Object.fromEntries(allMetrics),
    };
  }

  /**
   * Clear performance metrics and cache
   */
  clearMetrics(): void {
    this.performanceMetrics.clear();
    this.utilityCache.clear();
    logger.info('🧹 Performance metrics and cache cleared');
  }

  /**
   * Create performance execution context for integration bridge
   */
  createExecutionContext(
    ruleId: string,
    scanId?: string,
    integrationStrategy: 'supplement' | 'enhance' | 'validate' = 'enhance',
  ): UtilityExecutionContext {
    return {
      scanId,
      ruleId,
      startTime: Date.now(),
      utilitiesUsed: [],
      cacheHits: 0,
      cacheMisses: 0,
      errors: [],
      integrationStrategy,
    };
  }

  /**
   * Update execution context with performance data
   */
  updateExecutionContext(
    context: UtilityExecutionContext,
    result: UtilityAnalysisResult,
  ): UtilityExecutionContext {
    context.endTime = Date.now();
    context.utilitiesUsed = result.utilitiesUsed;
    context.errors = result.errors;

    // Get cache metrics from performance data
    const metrics = this.getPerformanceMetrics(context.ruleId);
    if (metrics && typeof metrics === 'object' && 'cacheHits' in metrics) {
      context.cacheHits = metrics.cacheHits;
      context.cacheMisses = metrics.cacheMisses;
    }

    return context;
  }

  /**
   * Execute utility analysis for a check
   */
  async executeUtilityAnalysis(
    page: Page,
    ruleId: string,
    scanId: string,
    customConfig?: Partial<UtilityIntegrationConfig>,
  ): Promise<UtilityAnalysisResult> {
    const startTime = Date.now();
    const config = { ...this.getUtilityConfig(ruleId), ...customConfig };
    const utilitiesUsed: string[] = [];
    const errors: string[] = [];
    const recommendations: string[] = [];

    logger.debug(`🔧 [${scanId}] Starting utility analysis for ${ruleId}`, { config });

    const result: UtilityAnalysisResult = {
      executionTime: 0,
      utilitiesUsed,
      errors,
      recommendations,
      utilityConfidence: 0,
      enhancedAccuracy: 0,
    };

    try {
      // Execute utilities based on configuration
      const analysisPromises: Promise<any>[] = [];

      if (config.enableSemanticValidation) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.semanticValidator.validateSemanticStructure(page),
            'semantic-validation',
            utilitiesUsed,
            errors,
          ),
        );
      }

      if (config.enablePatternValidation) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.patternLibrary.validateAccessibilityPatterns(page),
            'pattern-validation',
            utilitiesUsed,
            errors,
          ),
        );
      }

      if (config.enableContentQualityAnalysis) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.contentAnalyzer.analyzeContentQuality(page),
            'content-quality',
            utilitiesUsed,
            errors,
          ),
        );
      }

      if (config.enableFrameworkOptimization) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.frameworkOptimizer.analyzeModernFrameworks(page),
            'framework-optimization',
            utilitiesUsed,
            errors,
          ),
        );
      }

      if (config.enableComponentLibraryDetection) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.componentDetector.analyzeComponentLibraries(page),
            'component-library',
            utilitiesUsed,
            errors,
          ),
        );
      }

      if (config.enableCMSDetection) {
        analysisPromises.push(
          this.executeWithFallback(
            () => this.cmsDetector.analyzeHeadlessCMS(page),
            'cms-detection',
            utilitiesUsed,
            errors,
          ),
        );
      }

      // Execute all utilities in parallel with timeout
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(
          () => reject(new Error('Utility analysis timeout')),
          config.maxExecutionTime || 10000,
        ),
      );

      const analysisResults = (await Promise.race([
        Promise.allSettled(analysisPromises),
        timeoutPromise,
      ])) as PromiseSettledResult<any>[];

      // Process results
      let utilityIndex = 0;
      if (config.enableSemanticValidation && analysisResults[utilityIndex]) {
        const semanticResult = analysisResults[utilityIndex];
        if (semanticResult.status === 'fulfilled') {
          result.semanticAnalysis = semanticResult.value;
          recommendations.push(...(semanticResult.value?.recommendations || []));
        }
        utilityIndex++;
      }

      if (config.enablePatternValidation && analysisResults[utilityIndex]) {
        const patternResult = analysisResults[utilityIndex];
        if (patternResult.status === 'fulfilled') {
          result.patternAnalysis = patternResult.value;
          recommendations.push(...(patternResult.value?.recommendations || []));
        }
        utilityIndex++;
      }

      if (config.enableContentQualityAnalysis && analysisResults[utilityIndex]) {
        const contentResult = analysisResults[utilityIndex];
        if (contentResult.status === 'fulfilled') {
          result.contentQualityAnalysis = contentResult.value;
          recommendations.push(...(contentResult.value?.summary?.priorityRecommendations || []));
        }
        utilityIndex++;
      }

      if (config.enableFrameworkOptimization && analysisResults[utilityIndex]) {
        const frameworkResult = analysisResults[utilityIndex];
        if (frameworkResult.status === 'fulfilled') {
          result.frameworkAnalysis = frameworkResult.value;
          recommendations.push(...(frameworkResult.value?.recommendations || []));
        }
        utilityIndex++;
      }

      if (config.enableComponentLibraryDetection && analysisResults[utilityIndex]) {
        const componentResult = analysisResults[utilityIndex];
        if (componentResult.status === 'fulfilled') {
          result.componentLibraryAnalysis = componentResult.value;
          recommendations.push(...(componentResult.value?.recommendations || []));
        }
        utilityIndex++;
      }

      if (config.enableCMSDetection && analysisResults[utilityIndex]) {
        const cmsResult = analysisResults[utilityIndex];
        if (cmsResult.status === 'fulfilled') {
          result.cmsAnalysis = cmsResult.value;
          recommendations.push(...(cmsResult.value?.recommendations || []));
        }
        utilityIndex++;
      }

      // Calculate confidence and accuracy metrics
      result.utilityConfidence = this.calculateUtilityConfidence(result, utilitiesUsed);
      result.enhancedAccuracy = this.calculateEnhancedAccuracy(result, config);

      result.executionTime = Date.now() - startTime;

      logger.debug(`✅ [${scanId}] Utility analysis completed for ${ruleId}`, {
        utilitiesUsed: result.utilitiesUsed,
        confidence: result.utilityConfidence,
        accuracy: result.enhancedAccuracy,
        executionTime: result.executionTime,
      });

      return result;
    } catch (error) {
      logger.error(`❌ [${scanId}] Utility analysis failed for ${ruleId}:`, error);
      errors.push(
        `Utility analysis error: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );

      result.executionTime = Date.now() - startTime;
      return result;
    }
  }

  /**
   * Enhance check results with utility analysis
   */
  enhanceCheckResult(
    originalResult: any,
    utilityAnalysis: UtilityAnalysisResult,
    ruleId: string,
  ): CheckEnhancementResult {
    const enhancement: CheckEnhancementResult = {
      originalScore: originalResult.score,
      enhancedScore: originalResult.score,
      confidenceBoost: 0,
      additionalEvidence: [],
      utilityRecommendations: utilityAnalysis.recommendations,
      frameworkSpecificGuidance: [],
    };

    // Apply utility-based enhancements
    if (utilityAnalysis.semanticAnalysis) {
      enhancement.confidenceBoost += utilityAnalysis.semanticAnalysis.confidence * 0.2;
      enhancement.additionalEvidence.push({
        type: 'semantic-analysis',
        data: utilityAnalysis.semanticAnalysis,
      });
    }

    if (utilityAnalysis.patternAnalysis) {
      enhancement.confidenceBoost += utilityAnalysis.patternAnalysis.overallScore * 0.001;
      enhancement.additionalEvidence.push({
        type: 'pattern-analysis',
        data: utilityAnalysis.patternAnalysis,
      });
    }

    if (utilityAnalysis.frameworkAnalysis) {
      enhancement.frameworkSpecificGuidance.push(
        ...utilityAnalysis.frameworkAnalysis.recommendations,
      );
      enhancement.additionalEvidence.push({
        type: 'framework-analysis',
        data: utilityAnalysis.frameworkAnalysis,
      });
    }

    // Calculate enhanced score (conservative approach)
    const confidenceMultiplier = Math.min(1.1, 1 + enhancement.confidenceBoost);
    enhancement.enhancedScore = Math.min(
      originalResult.maxScore,
      Math.round(originalResult.score * confidenceMultiplier),
    );

    return enhancement;
  }

  /**
   * Execute utility with fallback handling
   */
  private async executeWithFallback<T>(
    utilityFunction: () => Promise<T>,
    utilityName: string,
    utilitiesUsed: string[],
    errors: string[],
  ): Promise<T | null> {
    try {
      const result = await utilityFunction();
      utilitiesUsed.push(utilityName);
      return result;
    } catch (error) {
      logger.warn(`⚠️ Utility ${utilityName} failed, continuing with fallback`, error);
      errors.push(`${utilityName}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      return null;
    }
  }

  /**
   * Calculate utility confidence score
   */
  private calculateUtilityConfidence(
    result: UtilityAnalysisResult,
    utilitiesUsed: string[],
  ): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence based on successful utility executions
    confidence += utilitiesUsed.length * 0.1;

    // Factor in specific utility confidence scores
    if (result.semanticAnalysis?.confidence) {
      confidence += result.semanticAnalysis.confidence * 0.2;
    }

    if (result.patternAnalysis?.overallScore) {
      confidence += (result.patternAnalysis.overallScore / 100) * 0.15;
    }

    return Math.min(1.0, confidence);
  }

  /**
   * Calculate enhanced accuracy improvement
   */
  private calculateEnhancedAccuracy(
    result: UtilityAnalysisResult,
    config: UtilityIntegrationConfig,
  ): number {
    let accuracy = 0;

    // Base accuracy improvement from utility usage
    if (config.enableSemanticValidation && result.semanticAnalysis) accuracy += 0.15;
    if (config.enablePatternValidation && result.patternAnalysis) accuracy += 0.2;
    if (config.enableContentQualityAnalysis && result.contentQualityAnalysis) accuracy += 0.1;
    if (config.enableFrameworkOptimization && result.frameworkAnalysis) accuracy += 0.25;
    if (config.enableComponentLibraryDetection && result.componentLibraryAnalysis) accuracy += 0.2;
    if (config.enableCMSDetection && result.cmsAnalysis) accuracy += 0.1;

    return Math.min(1.0, accuracy);
  }

  /**
   * Clear utility cache
   */
  clearCache(): void {
    this.utilityCache.clear();
    logger.debug('🧹 Utility cache cleared');
  }
}

export default UtilityIntegrationManager;
