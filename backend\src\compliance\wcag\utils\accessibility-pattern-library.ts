/**
 * Accessibility Pattern Library
 * Comprehensive library of accessibility patterns with recognition and validation capabilities
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface AccessibilityPatternDefinition {
  id: string;
  name: string;
  description: string;
  category: 'navigation' | 'forms' | 'content' | 'interactive' | 'media' | 'structure';
  wcagCriteria: string[];
  level: 'A' | 'AA' | 'AAA';
  selectors: string[];
  requiredAttributes: string[];
  optionalAttributes: string[];
  requiredChildren?: string[];
  forbiddenChildren?: string[];
  validation: {
    rules: PatternValidationRule[];
    customValidator?: string; // Function name for custom validation
  };
  examples: {
    good: string[];
    bad: string[];
  };
  recommendations: string[];
  bestPractices: string[];
  commonMistakes: string[];
}

export interface PatternValidationRule {
  type: 'attribute' | 'content' | 'structure' | 'behavior' | 'custom';
  property: string;
  condition: 'required' | 'forbidden' | 'conditional' | 'recommended';
  value?: string | RegExp;
  message: string;
  severity: 'error' | 'warning' | 'info';
}

export interface PatternDetectionResult {
  pattern: AccessibilityPatternDefinition;
  elements: PatternElementMatch[];
  isValid: boolean;
  confidence: number;
  issues: PatternIssue[];
  recommendations: string[];
  score: number;
}

export interface PatternElementMatch {
  element: string;
  selector: string;
  attributes: Record<string, string>;
  textContent: string;
  isValid: boolean;
  issues: PatternIssue[];
}

export interface PatternIssue {
  type: 'missing-attribute' | 'invalid-value' | 'structural' | 'content' | 'behavior';
  severity: 'error' | 'warning' | 'info';
  message: string;
  element?: string;
  recommendation: string;
}

export interface PatternLibraryConfig {
  enableAllPatterns: boolean;
  enabledCategories: string[];
  enabledLevels: string[];
  strictMode: boolean;
  includeExperimental: boolean;
  customPatterns: AccessibilityPatternDefinition[];
}

export interface PatternAnalysisReport {
  totalPatterns: number;
  detectedPatterns: number;
  validPatterns: number;
  results: PatternDetectionResult[];
  categoryBreakdown: Record<
    string,
    {
      total: number;
      valid: number;
      score: number;
    }
  >;
  overallScore: number;
  criticalIssues: PatternIssue[];
  recommendations: string[];
  bestPractices: string[];
}

/**
 * Comprehensive accessibility pattern library and validator
 */
export class AccessibilityPatternLibrary {
  private static instance: AccessibilityPatternLibrary;
  private patterns: Map<string, AccessibilityPatternDefinition> = new Map();
  private config: PatternLibraryConfig;

  private constructor(config?: Partial<PatternLibraryConfig>) {
    this.config = {
      enableAllPatterns: config?.enableAllPatterns ?? true,
      enabledCategories: config?.enabledCategories || [
        'navigation',
        'forms',
        'content',
        'interactive',
        'media',
        'structure',
      ],
      enabledLevels: config?.enabledLevels || ['A', 'AA', 'AAA'],
      strictMode: config?.strictMode ?? false,
      includeExperimental: config?.includeExperimental ?? false,
      customPatterns: config?.customPatterns || [],
    };

    this.initializePatternLibrary();
    this.loadCustomPatterns();

    logger.info('📚 Accessibility Pattern Library initialized', {
      totalPatterns: this.patterns.size,
      enabledCategories: this.config.enabledCategories.length,
      strictMode: this.config.strictMode,
    });
  }

  static getInstance(config?: Partial<PatternLibraryConfig>): AccessibilityPatternLibrary {
    if (!AccessibilityPatternLibrary.instance) {
      AccessibilityPatternLibrary.instance = new AccessibilityPatternLibrary(config);
    }
    return AccessibilityPatternLibrary.instance;
  }

  /**
   * Analyze page for accessibility patterns
   */
  async analyzePatterns(page: Page): Promise<PatternAnalysisReport> {
    logger.debug('🔍 Starting accessibility pattern analysis');

    // Inject pattern detection functions
    await this.injectPatternDetectionFunctions(page);

    // Get enabled patterns
    const enabledPatterns = this.getEnabledPatterns();

    // Detect and validate patterns
    const results: PatternDetectionResult[] = [];

    for (const pattern of enabledPatterns) {
      const result = await this.detectAndValidatePattern(page, pattern);
      if (result.elements.length > 0) {
        results.push(result);
      }
    }

    // Generate comprehensive report
    const report = this.generateAnalysisReport(results, enabledPatterns);

    logger.info(`✅ Pattern analysis completed`, {
      detectedPatterns: report.detectedPatterns,
      validPatterns: report.validPatterns,
      overallScore: report.overallScore,
    });

    return report;
  }

  /**
   * Get pattern by ID
   */
  getPattern(id: string): AccessibilityPatternDefinition | undefined {
    return this.patterns.get(id);
  }

  /**
   * Get all patterns by category
   */
  getPatternsByCategory(category: string): AccessibilityPatternDefinition[] {
    return Array.from(this.patterns.values()).filter((pattern) => pattern.category === category);
  }

  /**
   * Add custom pattern
   */
  addCustomPattern(pattern: AccessibilityPatternDefinition): void {
    this.patterns.set(pattern.id, pattern);
    logger.debug(`Added custom pattern: ${pattern.id}`);
  }

  /**
   * Initialize core accessibility patterns
   */
  private initializePatternLibrary(): void {
    const corePatterns: AccessibilityPatternDefinition[] = [
      // Navigation Patterns
      {
        id: 'skip-navigation',
        name: 'Skip Navigation Links',
        description: 'Provides skip links for keyboard navigation',
        category: 'navigation',
        wcagCriteria: ['2.4.1'],
        level: 'A',
        selectors: ['a[href^="#"]', '[role="navigation"] a'],
        requiredAttributes: ['href'],
        optionalAttributes: ['aria-label', 'title'],
        validation: {
          rules: [
            {
              type: 'attribute',
              property: 'href',
              condition: 'required',
              message: 'Skip link must have href attribute',
              severity: 'error',
            },
            {
              type: 'content',
              property: 'textContent',
              condition: 'required',
              message: 'Skip link must have descriptive text',
              severity: 'error',
            },
          ],
        },
        examples: {
          good: ['<a href="#main">Skip to main content</a>'],
          bad: ['<a href="#">Skip</a>', '<a href="#main"></a>'],
        },
        recommendations: [
          'Provide skip links at the beginning of the page',
          'Use descriptive text that explains the destination',
          'Ensure skip links are keyboard accessible',
        ],
        bestPractices: [
          'Place skip links as the first focusable elements',
          'Make skip links visible on focus',
          'Test with keyboard navigation',
        ],
        commonMistakes: [
          'Empty or non-descriptive link text',
          'Skip links that are not keyboard accessible',
          'Missing skip links on complex pages',
        ],
      },

      // Form Patterns
      {
        id: 'form-labels',
        name: 'Form Input Labels',
        description: 'Form inputs with proper labeling',
        category: 'forms',
        wcagCriteria: ['1.3.1', '3.3.2'],
        level: 'A',
        selectors: ['input', 'select', 'textarea'],
        requiredAttributes: [],
        optionalAttributes: ['aria-label', 'aria-labelledby', 'aria-describedby'],
        validation: {
          rules: [
            {
              type: 'custom',
              property: 'labeling',
              condition: 'required',
              message: 'Form input must have an associated label',
              severity: 'error',
            },
          ],
          customValidator: 'validateFormLabeling',
        },
        examples: {
          good: [
            '<label for="email">Email Address</label><input type="email" id="email">',
            '<input type="text" aria-label="Search">',
          ],
          bad: ['<input type="email">', '<label>Email</label><input type="email">'],
        },
        recommendations: [
          'Use label elements with for attributes',
          'Provide aria-label for inputs without visible labels',
          'Use aria-describedby for additional help text',
        ],
        bestPractices: [
          'Place labels before inputs in the DOM',
          'Use clear, descriptive label text',
          'Group related inputs with fieldset and legend',
        ],
        commonMistakes: [
          'Missing labels entirely',
          'Labels not properly associated with inputs',
          'Using placeholder text as labels',
        ],
      },

      // Content Patterns
      {
        id: 'heading-hierarchy',
        name: 'Heading Hierarchy',
        description: 'Proper heading structure and hierarchy',
        category: 'structure',
        wcagCriteria: ['1.3.1', '2.4.6'],
        level: 'AA',
        selectors: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', '[role="heading"]'],
        requiredAttributes: [],
        optionalAttributes: ['aria-level'],
        validation: {
          rules: [
            {
              type: 'structure',
              property: 'hierarchy',
              condition: 'required',
              message: 'Heading hierarchy must be logical',
              severity: 'error',
            },
          ],
          customValidator: 'validateHeadingHierarchy',
        },
        examples: {
          good: ['<h1>Main Title</h1><h2>Section</h2><h3>Subsection</h3>'],
          bad: ['<h1>Title</h1><h3>Section</h3>', '<h2>Title</h2>'],
        },
        recommendations: [
          'Start with h1 and follow sequential order',
          'Use only one h1 per page',
          "Don't skip heading levels",
        ],
        bestPractices: [
          'Use headings to create document outline',
          'Make heading text descriptive',
          'Use CSS for visual styling, not heading levels',
        ],
        commonMistakes: [
          'Skipping heading levels',
          'Multiple h1 elements',
          'Using headings for visual styling only',
        ],
      },

      // Interactive Patterns
      {
        id: 'button-accessibility',
        name: 'Accessible Buttons',
        description: 'Buttons with proper accessibility attributes',
        category: 'interactive',
        wcagCriteria: ['2.1.1', '4.1.2'],
        level: 'A',
        selectors: ['button', '[role="button"]', 'input[type="button"]', 'input[type="submit"]'],
        requiredAttributes: [],
        optionalAttributes: ['aria-label', 'aria-describedby', 'aria-expanded', 'aria-pressed'],
        validation: {
          rules: [
            {
              type: 'content',
              property: 'accessibleName',
              condition: 'required',
              message: 'Button must have accessible name',
              severity: 'error',
            },
          ],
          customValidator: 'validateButtonAccessibility',
        },
        examples: {
          good: ['<button>Save Document</button>', '<button aria-label="Close dialog">×</button>'],
          bad: ['<button></button>', '<button>Click here</button>'],
        },
        recommendations: [
          'Provide descriptive button text',
          'Use aria-label for icon buttons',
          'Indicate button state with aria-pressed or aria-expanded',
        ],
        bestPractices: [
          'Make button purpose clear from text alone',
          'Ensure buttons are keyboard accessible',
          'Provide visual focus indicators',
        ],
        commonMistakes: [
          'Empty buttons without accessible names',
          'Generic button text like "Click here"',
          'Missing state information for toggle buttons',
        ],
      },

      // Media Patterns
      {
        id: 'image-alternatives',
        name: 'Image Alternative Text',
        description: 'Images with appropriate alternative text',
        category: 'media',
        wcagCriteria: ['1.1.1'],
        level: 'A',
        selectors: ['img'],
        requiredAttributes: ['alt'],
        optionalAttributes: ['aria-label', 'aria-describedby', 'role'],
        validation: {
          rules: [
            {
              type: 'attribute',
              property: 'alt',
              condition: 'required',
              message: 'Image must have alt attribute',
              severity: 'error',
            },
          ],
          customValidator: 'validateImageAlternatives',
        },
        examples: {
          good: [
            '<img src="chart.png" alt="Sales increased 25% from Q1 to Q2">',
            '<img src="decoration.png" alt="" role="presentation">',
          ],
          bad: ['<img src="chart.png">', '<img src="photo.jpg" alt="Image">'],
        },
        recommendations: [
          'Describe the content and function of images',
          'Use empty alt="" for decorative images',
          'Avoid redundant phrases like "image of"',
        ],
        bestPractices: [
          'Keep alt text concise but descriptive',
          'Consider context when writing alt text',
          'Use role="presentation" for decorative images',
        ],
        commonMistakes: [
          'Missing alt attributes',
          'Generic alt text like "image" or "photo"',
          "Alt text that doesn't describe the image purpose",
        ],
      },
    ];

    // Add core patterns to library
    corePatterns.forEach((pattern) => {
      this.patterns.set(pattern.id, pattern);
    });

    logger.debug(`Initialized ${corePatterns.length} core accessibility patterns`);
  }

  /**
   * Load custom patterns from configuration
   */
  private loadCustomPatterns(): void {
    this.config.customPatterns.forEach((pattern) => {
      this.patterns.set(pattern.id, pattern);
    });

    if (this.config.customPatterns.length > 0) {
      logger.debug(`Loaded ${this.config.customPatterns.length} custom patterns`);
    }
  }

  /**
   * Get enabled patterns based on configuration
   */
  private getEnabledPatterns(): AccessibilityPatternDefinition[] {
    return Array.from(this.patterns.values()).filter((pattern) => {
      // Check if category is enabled
      if (!this.config.enabledCategories.includes(pattern.category)) {
        return false;
      }

      // Check if level is enabled
      if (!this.config.enabledLevels.includes(pattern.level)) {
        return false;
      }

      return true;
    });
  }

  /**
   * Inject pattern detection functions into page
   */
  private async injectPatternDetectionFunctions(page: Page): Promise<void> {
    await page.evaluate(() => {
      (window as any).accessibilityPatternDetection = {
        // Get element selector for identification
        getElementSelector(element: Element): string {
          if (element.id) return `#${element.id}`;
          if (element.className) {
            const classes = element.className.split(' ').filter((c) => c.trim());
            if (classes.length > 0) return `.${classes[0]}`;
          }
          return element.tagName.toLowerCase();
        },

        // Get all attributes of an element
        getElementAttributes(element: Element): Record<string, string> {
          const attributes: Record<string, string> = {};
          for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            attributes[attr.name] = attr.value;
          }
          return attributes;
        },

        // Check if element is visible
        isElementVisible(element: Element): boolean {
          const style = window.getComputedStyle(element as HTMLElement);
          return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
        },

        // Find elements matching pattern selectors
        findPatternElements(selectors: string[]): Element[] {
          const elements: Element[] = [];

          selectors.forEach((selector) => {
            try {
              const found = document.querySelectorAll(selector);
              found.forEach((element) => {
                if (this.isElementVisible(element)) {
                  elements.push(element);
                }
              });
            } catch (error) {
              console.warn(`Invalid selector: ${selector}`, error);
            }
          });

          return elements;
        },

        // Custom validators for specific patterns
        validateFormLabeling(element: Element): { isValid: boolean; issues: any[] } {
          const issues: any[] = [];
          let isValid = true;

          // Check for label element
          const id = element.id;
          let hasLabel = false;

          if (id) {
            const label = document.querySelector(`label[for="${id}"]`);
            if (label) {
              hasLabel = true;
            }
          }

          // Check for aria-label
          if (!hasLabel && element.getAttribute('aria-label')) {
            hasLabel = true;
          }

          // Check for aria-labelledby
          if (!hasLabel && element.getAttribute('aria-labelledby')) {
            const labelledBy = element.getAttribute('aria-labelledby');
            if (labelledBy && document.getElementById(labelledBy!)) {
              hasLabel = true;
            }
          }

          if (!hasLabel) {
            isValid = false;
            issues.push({
              type: 'missing-attribute',
              severity: 'error',
              message: 'Form input lacks proper labeling',
              recommendation: 'Add a label element, aria-label, or aria-labelledby attribute',
            });
          }

          return { isValid, issues };
        },

        validateHeadingHierarchy(elements: Element[]): { isValid: boolean; issues: any[] } {
          const issues: any[] = [];
          let isValid = true;

          if (elements.length === 0) return { isValid, issues };

          // Extract heading levels
          const headings = elements.map((el) => {
            const tagName = el.tagName.toLowerCase();
            if (tagName.startsWith('h') && tagName.length === 2) {
              return parseInt(tagName.charAt(1));
            }

            const ariaLevel = el.getAttribute('aria-level');
            return ariaLevel ? parseInt(ariaLevel) : 1;
          });

          // Check for h1
          if (!headings.includes(1)) {
            isValid = false;
            issues.push({
              type: 'structural',
              severity: 'error',
              message: 'Page missing h1 heading',
              recommendation: 'Add a main h1 heading to the page',
            });
          }

          // Check for skipped levels
          for (let i = 1; i < headings.length; i++) {
            if (headings[i] > headings[i - 1] + 1) {
              isValid = false;
              issues.push({
                type: 'structural',
                severity: 'error',
                message: `Heading hierarchy skips from h${headings[i - 1]} to h${headings[i]}`,
                recommendation: 'Use sequential heading levels without skipping',
              });
            }
          }

          return { isValid, issues };
        },

        validateButtonAccessibility(element: Element): { isValid: boolean; issues: any[] } {
          const issues: any[] = [];
          let isValid = true;

          // Check for accessible name
          const textContent = element.textContent?.trim();
          const ariaLabel = element.getAttribute('aria-label');
          const ariaLabelledBy = element.getAttribute('aria-labelledby');

          let hasAccessibleName = false;

          if (textContent && textContent.length > 0) {
            hasAccessibleName = true;

            // Check for generic text
            const genericTexts = ['click here', 'read more', 'more', 'link', 'button'];
            if (genericTexts.includes(textContent.toLowerCase())) {
              issues.push({
                type: 'content',
                severity: 'warning',
                message: 'Button text is not descriptive',
                recommendation: 'Use more specific text that describes the button action',
              });
            }
          }

          if (!hasAccessibleName && ariaLabel) {
            hasAccessibleName = true;
          }

          if (!hasAccessibleName && ariaLabelledBy) {
            const labelElement = document.getElementById(ariaLabelledBy);
            if (labelElement) {
              hasAccessibleName = true;
            }
          }

          if (!hasAccessibleName) {
            isValid = false;
            issues.push({
              type: 'missing-attribute',
              severity: 'error',
              message: 'Button lacks accessible name',
              recommendation: 'Add text content, aria-label, or aria-labelledby attribute',
            });
          }

          return { isValid, issues };
        },
      };
    });
  }

  /**
   * Detect and validate a specific pattern
   */
  private async detectAndValidatePattern(
    page: Page,
    pattern: AccessibilityPatternDefinition,
  ): Promise<PatternDetectionResult> {
    const elements = await page.evaluate((patternData) => {
      const detection = (window as any).accessibilityPatternDetection;
      const foundElements = detection.findPatternElements(patternData.selectors);

      return foundElements.map((element: Element) => ({
        selector: detection.getElementSelector(element),
        attributes: detection.getElementAttributes(element),
        textContent: element.textContent?.trim() || '',
        tagName: element.tagName.toLowerCase(),
      }));
    }, pattern);

    // Validate each element
    const elementMatches: PatternElementMatch[] = [];
    const allIssues: PatternIssue[] = [];

    for (const elementData of elements) {
      const validation = await this.validatePatternElement(page, pattern, elementData);

      elementMatches.push({
        element: elementData.tagName,
        selector: elementData.selector,
        attributes: elementData.attributes,
        textContent: elementData.textContent,
        isValid: validation.isValid,
        issues: validation.issues,
      });

      allIssues.push(...validation.issues);
    }

    // Calculate overall validity and confidence
    const validElements = elementMatches.filter((em) => em.isValid).length;
    const isValid = elementMatches.length > 0 && validElements === elementMatches.length;
    const confidence = elementMatches.length > 0 ? validElements / elementMatches.length : 0;

    // Calculate score
    const score = this.calculatePatternScore(pattern, elementMatches, allIssues);

    // Generate recommendations
    const recommendations = this.generatePatternRecommendations(pattern, allIssues);

    return {
      pattern,
      elements: elementMatches,
      isValid,
      confidence,
      issues: allIssues,
      recommendations,
      score,
    };
  }

  /**
   * Validate a pattern element
   */
  private async validatePatternElement(
    page: Page,
    pattern: AccessibilityPatternDefinition,
    elementData: any,
  ): Promise<{ isValid: boolean; issues: PatternIssue[] }> {
    const issues: PatternIssue[] = [];
    let isValid = true;

    // Validate required attributes
    for (const attr of pattern.requiredAttributes) {
      if (!elementData.attributes[attr]) {
        isValid = false;
        issues.push({
          type: 'missing-attribute',
          severity: 'error',
          message: `Missing required attribute: ${attr}`,
          element: elementData.selector,
          recommendation: `Add ${attr} attribute to element`,
        });
      }
    }

    // Run custom validation if specified
    if (pattern.validation.customValidator) {
      const customValidation = await page.evaluate(
        (validatorName, selector) => {
          const element = document.querySelector(selector);
          if (element && (window as any).accessibilityPatternDetection[validatorName]) {
            return (window as any).accessibilityPatternDetection[validatorName](element);
          }
          return { isValid: true, issues: [] };
        },
        pattern.validation.customValidator,
        elementData.selector,
      );

      if (!customValidation.isValid) {
        isValid = false;
      }
      issues.push(...customValidation.issues);
    }

    // Validate pattern rules
    for (const rule of pattern.validation.rules) {
      if (rule.type === 'custom') continue; // Handled above

      const ruleValidation = this.validatePatternRule(rule, elementData);
      if (!ruleValidation.isValid) {
        isValid = false;
        issues.push({
          type: rule.type as any,
          severity: rule.severity,
          message: rule.message,
          element: elementData.selector,
          recommendation: `Fix ${rule.property} validation issue`,
        });
      }
    }

    return { isValid, issues };
  }

  /**
   * Validate a specific pattern rule
   */
  private validatePatternRule(rule: PatternValidationRule, elementData: any): { isValid: boolean } {
    switch (rule.type) {
      case 'attribute':
        if (rule.condition === 'required') {
          return { isValid: !!elementData.attributes[rule.property] };
        }
        if (rule.condition === 'forbidden') {
          return { isValid: !elementData.attributes[rule.property] };
        }
        break;

      case 'content':
        if (rule.condition === 'required') {
          return { isValid: !!elementData.textContent };
        }
        break;
    }

    return { isValid: true };
  }

  /**
   * Calculate pattern score
   */
  private calculatePatternScore(
    pattern: AccessibilityPatternDefinition,
    elements: PatternElementMatch[],
    issues: PatternIssue[],
  ): number {
    if (elements.length === 0) return 0;

    let score = 100;

    // Deduct for errors and warnings
    const errors = issues.filter((i) => i.severity === 'error').length;
    const warnings = issues.filter((i) => i.severity === 'warning').length;

    score -= errors * 20;
    score -= warnings * 10;

    // Bonus for following best practices
    const validElements = elements.filter((e) => e.isValid).length;
    const validityRatio = validElements / elements.length;
    score *= validityRatio;

    return Math.max(0, Math.round(score));
  }

  /**
   * Generate pattern-specific recommendations
   */
  private generatePatternRecommendations(
    pattern: AccessibilityPatternDefinition,
    issues: PatternIssue[],
  ): string[] {
    const recommendations = new Set<string>();

    // Add pattern-specific recommendations
    pattern.recommendations.forEach((rec) => recommendations.add(rec));

    // Add issue-specific recommendations
    issues.forEach((issue) => {
      if (issue.recommendation) {
        recommendations.add(issue.recommendation);
      }
    });

    return Array.from(recommendations).slice(0, 10);
  }

  /**
   * Generate comprehensive analysis report
   */
  private generateAnalysisReport(
    results: PatternDetectionResult[],
    enabledPatterns: AccessibilityPatternDefinition[],
  ): PatternAnalysisReport {
    const detectedPatterns = results.length;
    const validPatterns = results.filter((r) => r.isValid).length;

    // Calculate category breakdown
    const categoryBreakdown: Record<string, { total: number; valid: number; score: number }> = {};

    this.config.enabledCategories.forEach((category) => {
      const categoryResults = results.filter((r) => r.pattern.category === category);
      const validCategoryResults = categoryResults.filter((r) => r.isValid);
      const avgScore =
        categoryResults.length > 0
          ? categoryResults.reduce((sum, r) => sum + r.score, 0) / categoryResults.length
          : 0;

      categoryBreakdown[category] = {
        total: categoryResults.length,
        valid: validCategoryResults.length,
        score: Math.round(avgScore),
      };
    });

    // Calculate overall score
    const overallScore =
      results.length > 0
        ? Math.round(results.reduce((sum, r) => sum + r.score, 0) / results.length)
        : 100;

    // Collect critical issues
    const criticalIssues = results
      .flatMap((r) => r.issues)
      .filter((issue) => issue.severity === 'error')
      .slice(0, 10);

    // Generate recommendations
    const recommendations = new Set<string>();
    results.forEach((result) => {
      result.recommendations.forEach((rec) => recommendations.add(rec));
    });

    // Generate best practices
    const bestPractices = new Set<string>();
    results.forEach((result) => {
      result.pattern.bestPractices.forEach((bp) => bestPractices.add(bp));
    });

    return {
      totalPatterns: enabledPatterns.length,
      detectedPatterns,
      validPatterns,
      results,
      categoryBreakdown,
      overallScore,
      criticalIssues,
      recommendations: Array.from(recommendations).slice(0, 15),
      bestPractices: Array.from(bestPractices).slice(0, 10),
    };
  }
}

export default AccessibilityPatternLibrary;
